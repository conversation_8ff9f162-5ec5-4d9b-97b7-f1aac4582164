{"name": "studio-erp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@prisma/client": "^6.12.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/qrcode": "^1.5.5", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "face-api.js": "^0.22.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "multer": "^2.0.2", "next": "15.4.2", "prisma": "^6.12.0", "qrcode": "^1.5.4", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.2", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}