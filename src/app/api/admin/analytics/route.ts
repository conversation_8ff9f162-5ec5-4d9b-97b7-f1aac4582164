import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { authenticateRequest, authorizeRole } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30'; // days
    const startDate = new Date(Date.now() - parseInt(period) * 24 * 60 * 60 * 1000);

    // Platform-wide analytics
    const [
      totalStudios,
      activeStudios,
      totalClients,
      totalPhotos,
      totalRevenue,
      newStudiosThisPeriod,
      newClientsThisPeriod,
      photosUploadedThisPeriod,
      revenueThisPeriod,
      storageUsage,
      topStudios,
      subscriptionBreakdown,
      dailyStats,
    ] = await Promise.all([
      // Total studios
      prisma.studio.count(),
      
      // Active studios (logged in within last 30 days)
      prisma.studio.count({
        where: {
          lastLogin: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          },
        },
      }),
      
      // Total clients
      prisma.client.count(),
      
      // Total photos
      prisma.photo.count(),
      
      // Total revenue
      prisma.billingHistory.aggregate({
        _sum: { amount: true },
        where: { status: 'paid' },
      }),
      
      // New studios this period
      prisma.studio.count({
        where: {
          createdAt: { gte: startDate },
        },
      }),
      
      // New clients this period
      prisma.client.count({
        where: {
          createdAt: { gte: startDate },
        },
      }),
      
      // Photos uploaded this period
      prisma.photo.count({
        where: {
          uploadedAt: { gte: startDate },
        },
      }),
      
      // Revenue this period
      prisma.billingHistory.aggregate({
        _sum: { amount: true },
        where: {
          status: 'paid',
          createdAt: { gte: startDate },
        },
      }),
      
      // Storage usage
      prisma.studio.aggregate({
        _sum: { storageUsed: true },
      }),
      
      // Top studios by photo count
      prisma.studio.findMany({
        select: {
          id: true,
          name: true,
          businessName: true,
          _count: {
            select: {
              photos: true,
              clients: true,
            },
          },
        },
        orderBy: {
          photos: {
            _count: 'desc',
          },
        },
        take: 10,
      }),
      
      // Subscription plan breakdown
      prisma.subscriptionPlan.findMany({
        select: {
          name: true,
          price: true,
          _count: {
            select: {
              subscriptions: true,
            },
          },
        },
      }),
      
      // Daily stats for the period
      prisma.$queryRaw`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as studios_registered
        FROM studios 
        WHERE created_at >= ${startDate}
        GROUP BY DATE(created_at)
        ORDER BY date
      `,
    ]);

    const analytics = {
      overview: {
        totalStudios,
        activeStudios,
        totalClients,
        totalPhotos,
        totalRevenue: totalRevenue._sum.amount || 0,
        storageUsed: (storageUsage._sum.storageUsed || BigInt(0)).toString(),
      },
      periodStats: {
        period: parseInt(period),
        newStudios: newStudiosThisPeriod,
        newClients: newClientsThisPeriod,
        photosUploaded: photosUploadedThisPeriod,
        revenue: revenueThisPeriod._sum.amount || 0,
      },
      topStudios,
      subscriptionBreakdown,
      dailyStats,
      growth: {
        studiosGrowthRate: totalStudios > 0 ? (newStudiosThisPeriod / totalStudios) * 100 : 0,
        clientsGrowthRate: totalClients > 0 ? (newClientsThisPeriod / totalClients) * 100 : 0,
        revenueGrowthRate: 0, // Would need previous period data to calculate
      },
    };

    return NextResponse.json({
      success: true,
      data: analytics,
    });
  } catch (error) {
    console.error('Error fetching admin analytics:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Export analytics data
export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { format = 'csv', period = '30' } = body;

    // Get data for export
    const startDate = new Date(Date.now() - parseInt(period) * 24 * 60 * 60 * 1000);
    
    const studios = await prisma.studio.findMany({
      select: {
        id: true,
        name: true,
        businessName: true,
        email: true,
        createdAt: true,
        isActive: true,
        isApproved: true,
        storageUsed: true,
        _count: {
          select: {
            photos: true,
            clients: true,
          },
        },
        subscription: {
          select: {
            plan: {
              select: {
                name: true,
                price: true,
              },
            },
          },
        },
      },
      where: {
        createdAt: { gte: startDate },
      },
    });

    if (format === 'csv') {
      // Generate CSV
      const csvHeaders = [
        'Studio ID',
        'Name',
        'Business Name',
        'Email',
        'Registration Date',
        'Status',
        'Approved',
        'Photos Count',
        'Clients Count',
        'Storage Used (GB)',
        'Subscription Plan',
        'Plan Price',
      ];

      const csvRows = studios.map(studio => [
        studio.id,
        studio.name,
        studio.businessName || '',
        studio.email,
        studio.createdAt.toISOString().split('T')[0],
        studio.isActive ? 'Active' : 'Inactive',
        studio.isApproved ? 'Yes' : 'No',
        studio._count.photos,
        studio._count.clients,
        (Number(studio.storageUsed) / (1024 * 1024 * 1024)).toFixed(2),
        studio.subscription?.plan?.name || 'Free',
        studio.subscription?.plan?.price || 0,
      ]);

      const csvContent = [csvHeaders, ...csvRows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n');

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="studios-report-${period}days.csv"`,
        },
      });
    }

    // Return JSON format
    return NextResponse.json({
      success: true,
      data: studios,
      exportedAt: new Date().toISOString(),
      period: parseInt(period),
    });
  } catch (error) {
    console.error('Error exporting analytics:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
