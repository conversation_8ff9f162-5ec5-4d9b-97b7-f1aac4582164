import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { verifyPassword, generateToken } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { accessCode, password } = body;

    if (!accessCode || !password) {
      return NextResponse.json(
        { success: false, message: 'Access code and password are required' },
        { status: 400 }
      );
    }

    // Find client by access link or QR code
    const client = await prisma.client.findFirst({
      where: {
        OR: [
          { accessLink: accessCode },
          { qrCode: accessCode },
        ],
        isActive: true,
      },
      include: {
        studio: {
          select: {
            name: true,
            businessName: true,
            isActive: true,
            isApproved: true,
          },
        },
      },
    });

    if (!client) {
      return NextResponse.json(
        { success: false, message: 'Invalid access code' },
        { status: 404 }
      );
    }

    // Check if studio is active and approved
    if (!client.studio.isActive || !client.studio.isApproved) {
      return NextResponse.json(
        { success: false, message: 'Studio account is not active' },
        { status: 403 }
      );
    }

    // Verify password
    const isPasswordValid = await verifyPassword(password, client.password);
    if (!isPasswordValid) {
      return NextResponse.json(
        { success: false, message: 'Invalid password' },
        { status: 401 }
      );
    }

    // Generate client token
    const token = generateToken({
      userId: client.id,
      email: client.email,
      role: 'client',
    });

    // Update last access
    await prisma.client.update({
      where: { id: client.id },
      data: { lastAccess: new Date() },
    });

    // Log access
    await prisma.accessLog.create({
      data: {
        clientId: client.id,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        action: 'login',
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Access granted',
      token,
      client: {
        id: client.id,
        name: client.name,
        email: client.email,
        studio: {
          name: client.studio.businessName || client.studio.name,
        },
      },
    });
  } catch (error) {
    console.error('Client access error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
