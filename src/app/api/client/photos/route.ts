import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { authenticateRequest, authorizeR<PERSON> } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['client'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const eventId = searchParams.get('eventId');

    const skip = (page - 1) * limit;

    // Build where clause - only show matched photos for this client
    const where: any = {
      clientId: user.id,
      isMatched: true,
    };

    if (eventId) {
      where.eventId = eventId;
    }

    const [photos, total] = await Promise.all([
      prisma.photo.findMany({
        where,
        skip,
        take: limit,
        include: {
          event: {
            select: {
              id: true,
              name: true,
              date: true,
              location: true,
            },
          },
          favorites: {
            where: { clientId: user.id },
            select: { id: true },
          },
          comments: {
            where: { clientId: user.id },
            select: {
              id: true,
              content: true,
              createdAt: true,
            },
          },
          _count: {
            select: {
              favorites: true,
              comments: true,
            },
          },
        },
        orderBy: { uploadedAt: 'desc' },
      }),
      prisma.photo.count({ where }),
    ]);

    // Add isFavorited flag to each photo and convert BigInt to string
    const photosWithFavorites = photos.map(photo => ({
      ...photo,
      size: photo.size.toString(),
      isFavorited: photo.favorites.length > 0,
      favorites: undefined, // Remove the favorites array, we only need the flag
    }));

    return NextResponse.json({
      success: true,
      data: photosWithFavorites,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching client photos:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Add or remove favorite
export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['client'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { photoId, action } = body;

    if (!photoId || !action) {
      return NextResponse.json(
        { success: false, message: 'Photo ID and action are required' },
        { status: 400 }
      );
    }

    // Verify photo belongs to this client
    const photo = await prisma.photo.findFirst({
      where: {
        id: photoId,
        clientId: user.id,
        isMatched: true,
      },
    });

    if (!photo) {
      return NextResponse.json(
        { success: false, message: 'Photo not found' },
        { status: 404 }
      );
    }

    if (action === 'favorite') {
      // Add to favorites
      await prisma.favorite.upsert({
        where: {
          photoId_clientId: {
            photoId,
            clientId: user.id,
          },
        },
        update: {},
        create: {
          photoId,
          clientId: user.id,
        },
      });

      // Log action
      await prisma.accessLog.create({
        data: {
          clientId: user.id,
          ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
          action: 'favorite_photo',
        },
      });

      return NextResponse.json({
        success: true,
        message: 'Photo added to favorites',
      });
    } else if (action === 'unfavorite') {
      // Remove from favorites
      await prisma.favorite.deleteMany({
        where: {
          photoId,
          clientId: user.id,
        },
      });

      return NextResponse.json({
        success: true,
        message: 'Photo removed from favorites',
      });
    } else {
      return NextResponse.json(
        { success: false, message: 'Invalid action' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error updating photo favorite:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
