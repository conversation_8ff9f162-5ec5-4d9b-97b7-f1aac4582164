import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { authenticateRequest, authorizeRole } from '@/lib/auth';
import { doFacesMatch } from '@/lib/face-recognition';

export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeR<PERSON>(user, ['client'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { faceDescriptor } = body;

    if (!faceDescriptor || !Array.isArray(faceDescriptor)) {
      return NextResponse.json(
        { success: false, message: 'Valid face descriptor is required' },
        { status: 400 }
      );
    }

    // Get client's stored face descriptors
    const storedDescriptors = await prisma.faceDescriptor.findMany({
      where: { clientId: user.id },
    });

    if (storedDescriptors.length === 0) {
      // No face descriptors stored, allow access but suggest enrollment
      return NextResponse.json({
        success: true,
        verified: false,
        message: 'No face profile found. You can still access your photos, but consider enrolling your face for enhanced security.',
        requiresEnrollment: true,
      });
    }

    // Check if the provided descriptor matches any stored descriptor
    let bestMatch = false;
    let bestConfidence = 0;

    for (const stored of storedDescriptors) {
      const isMatch = doFacesMatch(faceDescriptor, stored.descriptor as number[]);
      if (isMatch) {
        bestMatch = true;
        bestConfidence = Math.max(bestConfidence, stored.confidence);
        break;
      }
    }

    if (bestMatch) {
      // Log successful verification
      await prisma.accessLog.create({
        data: {
          clientId: user.id,
          ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
          action: 'face_verification_success',
        },
      });

      return NextResponse.json({
        success: true,
        verified: true,
        message: 'Face verification successful',
        confidence: bestConfidence,
      });
    } else {
      // Log failed verification
      await prisma.accessLog.create({
        data: {
          clientId: user.id,
          ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
          action: 'face_verification_failed',
        },
      });

      return NextResponse.json({
        success: false,
        verified: false,
        message: 'Face verification failed. Please try again or contact your photographer.',
      });
    }
  } catch (error) {
    console.error('Face verification error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Enroll a new face descriptor for the client
export async function PUT(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['client'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { faceDescriptor, boundingBox, confidence } = body;

    if (!faceDescriptor || !Array.isArray(faceDescriptor)) {
      return NextResponse.json(
        { success: false, message: 'Valid face descriptor is required' },
        { status: 400 }
      );
    }

    // Create a new face descriptor for the client
    const newDescriptor = await prisma.faceDescriptor.create({
      data: {
        clientId: user.id,
        photoId: 'enrollment', // Special ID for enrollment
        descriptor: faceDescriptor,
        confidence: confidence || 0.9,
        boundingBox: boundingBox || { x: 0, y: 0, width: 100, height: 100 },
      },
    });

    // Log enrollment
    await prisma.accessLog.create({
      data: {
        clientId: user.id,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        action: 'face_enrollment',
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Face profile enrolled successfully',
      descriptorId: newDescriptor.id,
    });
  } catch (error) {
    console.error('Face enrollment error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
