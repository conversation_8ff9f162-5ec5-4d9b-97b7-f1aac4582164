import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { authenticateRequest, authorizeR<PERSON> } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeR<PERSON>(user, ['studio'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get studio with subscription details
    const studio = await prisma.studio.findUnique({
      where: { id: user.id },
      include: {
        subscription: {
          include: {
            plan: true,
          },
        },
      },
    });

    if (!studio) {
      return NextResponse.json(
        { success: false, message: 'Studio not found' },
        { status: 404 }
      );
    }

    // Get counts and statistics
    const [
      totalClients,
      totalPhotos,
      matchedPhotos,
      recentUploads,
      recentActivity,
    ] = await Promise.all([
      // Total clients
      prisma.client.count({
        where: { studioId: user.id, isActive: true },
      }),
      
      // Total photos
      prisma.photo.count({
        where: { studioId: user.id },
      }),
      
      // Matched photos
      prisma.photo.count({
        where: { studioId: user.id, isMatched: true },
      }),
      
      // Recent uploads (last 7 days)
      prisma.photo.count({
        where: {
          studioId: user.id,
          uploadedAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          },
        },
      }),
      
      // Recent activity (last 10 activities)
      prisma.photo.findMany({
        where: { studioId: user.id },
        include: {
          client: {
            select: { name: true },
          },
        },
        orderBy: { uploadedAt: 'desc' },
        take: 10,
      }),
    ]);

    // Calculate storage usage percentage
    const storageLimit = studio.subscription?.plan?.storageLimit || BigInt(10 * 1024 * 1024 * 1024); // Default 10GB
    const storagePercentage = Math.round((Number(studio.storageUsed) / Number(storageLimit)) * 100);

    // Get recent client access logs
    const recentAccess = await prisma.accessLog.findMany({
      where: {
        client: {
          studioId: user.id,
        },
      },
      include: {
        client: {
          select: { name: true },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: 5,
    });

    const stats = {
      storageUsed: studio.storageUsed.toString(),
      storageLimit: storageLimit.toString(),
      storagePercentage,
      totalClients,
      totalPhotos,
      matchedPhotos,
      unmatchedPhotos: totalPhotos - matchedPhotos,
      recentUploads,
      subscriptionStatus: studio.subscription?.status || 'free',
      planName: studio.subscription?.plan?.name || 'Free',
    };

    const activities = [
      ...recentActivity.map(photo => ({
        type: 'upload',
        description: `Uploaded photo ${photo.originalName}`,
        client: photo.client?.name || 'Unknown',
        timestamp: photo.uploadedAt,
      })),
      ...recentAccess.map(access => ({
        type: 'access',
        description: `${access.client.name} ${access.action}`,
        client: access.client.name,
        timestamp: access.createdAt,
      })),
    ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, 10);

    return NextResponse.json({
      success: true,
      data: {
        studio: {
          id: studio.id,
          name: studio.name,
          businessName: studio.businessName,
          email: studio.email,
          phone: studio.phone,
          logo: studio.logo,
        },
        stats,
        activities,
      },
    });
  } catch (error) {
    console.error('Error fetching studio dashboard:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
