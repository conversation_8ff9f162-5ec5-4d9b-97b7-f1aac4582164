import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { authenticateRequest, authorizeRole } from '@/lib/auth';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { generateUniqueFilename, isImageFile } from '@/lib/utils';

export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['studio'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    const eventId = formData.get('eventId') as string;

    if (!files || files.length === 0) {
      return NextResponse.json(
        { success: false, message: 'No files provided' },
        { status: 400 }
      );
    }

    // Check studio storage limit
    const studio = await prisma.studio.findUnique({
      where: { id: user.id },
      include: {
        subscription: {
          include: {
            plan: true,
          },
        },
      },
    });

    if (!studio) {
      return NextResponse.json(
        { success: false, message: 'Studio not found' },
        { status: 404 }
      );
    }

    const storageLimit = studio.subscription?.plan?.storageLimit || BigInt(10 * 1024 * 1024 * 1024); // Default 10GB
    const totalFileSize = files.reduce((total, file) => total + file.size, 0);

    if (studio.storageUsed + BigInt(totalFileSize) > storageLimit) {
      return NextResponse.json(
        { success: false, message: 'Storage limit exceeded' },
        { status: 400 }
      );
    }

    // Verify event belongs to studio if provided
    if (eventId) {
      const event = await prisma.event.findFirst({
        where: {
          id: eventId,
          studioId: user.id,
        },
      });

      if (!event) {
        return NextResponse.json(
          { success: false, message: 'Event not found' },
          { status: 404 }
        );
      }
    }

    const uploadResults = [];
    const uploadDir = join(process.cwd(), 'public', 'uploads', user.id);

    // Ensure upload directory exists
    try {
      await mkdir(uploadDir, { recursive: true });
    } catch (error) {
      // Directory might already exist
    }

    for (const file of files) {
      try {
        // Validate file type
        if (!isImageFile(file.name)) {
          uploadResults.push({
            filename: file.name,
            success: false,
            error: 'Invalid file type. Only images are allowed.',
          });
          continue;
        }

        // Generate unique filename
        const uniqueFilename = generateUniqueFilename(file.name);
        const filePath = join(uploadDir, uniqueFilename);
        const relativePath = `/uploads/${user.id}/${uniqueFilename}`;

        // Save file to disk
        const bytes = await file.arrayBuffer();
        const buffer = Buffer.from(bytes);
        await writeFile(filePath, buffer);

        // Save to database
        const photo = await prisma.photo.create({
          data: {
            filename: uniqueFilename,
            originalName: file.name,
            path: relativePath,
            size: BigInt(file.size),
            mimeType: file.type,
            studioId: user.id,
            eventId: eventId || null,
            isProcessed: false,
            isMatched: false,
          },
        });

        uploadResults.push({
          filename: file.name,
          success: true,
          photoId: photo.id,
          path: relativePath,
        });

        // Update studio storage usage
        await prisma.studio.update({
          where: { id: user.id },
          data: {
            storageUsed: {
              increment: BigInt(file.size),
            },
          },
        });

        // TODO: Queue for face detection processing
        // In a real implementation, you would:
        // 1. Add to a job queue for background processing
        // 2. Process face detection asynchronously
        // 3. Update photo.isProcessed when complete

      } catch (error) {
        console.error(`Error uploading file ${file.name}:`, error);
        uploadResults.push({
          filename: file.name,
          success: false,
          error: 'Upload failed',
        });
      }
    }

    const successCount = uploadResults.filter(result => result.success).length;
    const failCount = uploadResults.length - successCount;

    return NextResponse.json({
      success: true,
      message: `${successCount} files uploaded successfully${failCount > 0 ? `, ${failCount} failed` : ''}`,
      results: uploadResults,
      stats: {
        total: uploadResults.length,
        success: successCount,
        failed: failCount,
      },
    });
  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Get upload progress/status
export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['studio'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get recent uploads and processing status
    const recentUploads = await prisma.photo.findMany({
      where: { studioId: user.id },
      orderBy: { uploadedAt: 'desc' },
      take: 20,
      include: {
        event: {
          select: {
            name: true,
          },
        },
      },
    });

    const stats = {
      totalPhotos: await prisma.photo.count({
        where: { studioId: user.id },
      }),
      processedPhotos: await prisma.photo.count({
        where: { studioId: user.id, isProcessed: true },
      }),
      matchedPhotos: await prisma.photo.count({
        where: { studioId: user.id, isMatched: true },
      }),
      pendingProcessing: await prisma.photo.count({
        where: { studioId: user.id, isProcessed: false },
      }),
    };

    // Transform BigInt values to strings for JSON serialization
    const transformedUploads = recentUploads.map(upload => ({
      ...upload,
      size: upload.size.toString(),
    }));

    return NextResponse.json({
      success: true,
      data: {
        recentUploads: transformedUploads,
        stats,
      },
    });
  } catch (error) {
    console.error('Error fetching upload status:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
