"use client";

import { useState } from "react";
import {
  Bell,
  Check,
  X,
  Eye,
  Trash2,
  Filter,
  Search,
  Users,
  Camera,
  Download,
  Shield,
  CreditCard,
  AlertTriangle,
  CheckCircle,
  Info,
  Clock,
  Mail,
  MessageSquare,
  Send,
  Settings,
  Calendar,
  Edit,
  Plus,
  MoreHorizontal,
  Smartphone,
  Monitor,
  Globe,
  User,
  Star,
  Heart,
  Share2,
  Image,
  Zap,
  Activity,
  TrendingUp,
  BarChart3,
  Target,
  Lightbulb,
  Palette,
  Type,
  Layout,
  Code,
  Link,
  FileText,
  Layers,
  RefreshCw
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";
import StudioLayout from "@/components/studio/StudioLayout";
import { formatDate, timeAgo, cn } from "@/lib/utils";

interface Notification {
  id: string;
  type: 'email' | 'sms' | 'push' | 'in_app';
  title: string;
  message: string;
  recipientType: 'client' | 'studio' | 'admin' | 'all';
  recipients: string[];
  status: 'draft' | 'scheduled' | 'sent' | 'delivered' | 'failed';
  scheduledAt?: string;
  sentAt?: string;
  deliveredCount: number;
  openedCount: number;
  clickedCount: number;
  failedCount: number;
  templateId?: string;
  createdAt: string;
  createdBy: string;
}

interface NotificationTemplate {
  id: string;
  name: string;
  type: 'email' | 'sms' | 'push';
  category: 'welcome' | 'reminder' | 'update' | 'marketing' | 'system';
  subject?: string;
  content: string;
  variables: string[];
  isActive: boolean;
  usageCount: number;
  lastUsed?: string;
  createdAt: string;
  updatedAt: string;
}

interface AutomationRule {
  id: string;
  name: string;
  trigger: 'gallery_created' | 'photos_uploaded' | 'client_signup' | 'payment_due' | 'event_reminder';
  conditions: {
    field: string;
    operator: string;
    value: string;
  }[];
  actions: {
    type: 'send_notification' | 'send_email' | 'send_sms';
    templateId: string;
    delay?: number; // in minutes
  }[];
  isActive: boolean;
  executionCount: number;
  lastExecuted?: string;
  createdAt: string;
}

export default function NotificationsPage() {
  const [activeTab, setActiveTab] = useState("notifications");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [showCreateNotification, setShowCreateNotification] = useState(false);
  const [showTemplateEditor, setShowTemplateEditor] = useState(false);

  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      type: 'email',
      title: 'Wedding Gallery Ready',
      message: 'Your wedding photos are now available for viewing and download.',
      recipientType: 'client',
      recipients: ['<EMAIL>'],
      status: 'delivered',
      sentAt: '2024-01-20T10:00:00Z',
      deliveredCount: 1,
      openedCount: 1,
      clickedCount: 1,
      failedCount: 0,
      templateId: 'template_1',
      createdAt: '2024-01-20T09:30:00Z',
      createdBy: 'Studio Admin'
    },
    {
      id: '2',
      type: 'sms',
      title: 'Event Reminder',
      message: 'Reminder: Your photo session is tomorrow at 2 PM.',
      recipientType: 'client',
      recipients: ['+1234567890'],
      status: 'sent',
      sentAt: '2024-01-21T14:00:00Z',
      deliveredCount: 1,
      openedCount: 0,
      clickedCount: 0,
      failedCount: 0,
      createdAt: '2024-01-21T13:45:00Z',
      createdBy: 'Studio Admin'
    }
  ]);

  const [templates] = useState<NotificationTemplate[]>([
    {
      id: 'template_1',
      name: 'Gallery Ready Notification',
      type: 'email',
      category: 'update',
      subject: 'Your {{event_type}} photos are ready!',
      content: `Hi {{client_name}},

Great news! Your {{event_type}} photos from {{event_date}} are now ready for viewing.

You can access your gallery here: {{gallery_url}}

Gallery Details:
- Total Photos: {{photo_count}}
- Event: {{event_type}}
- Date: {{event_date}}

Best regards,
{{studio_name}}`,
      variables: ['client_name', 'event_type', 'event_date', 'gallery_url', 'photo_count', 'studio_name'],
      isActive: true,
      usageCount: 45,
      lastUsed: '2024-01-20T10:00:00Z',
      createdAt: '2024-01-01T10:00:00Z',
      updatedAt: '2024-01-15T14:30:00Z'
    },
    {
      id: 'template_2',
      name: 'Event Reminder SMS',
      type: 'sms',
      category: 'reminder',
      content: 'Hi {{client_name}}! Reminder: Your {{event_type}} session is {{time_until}} at {{event_time}}. Location: {{event_location}}. See you soon! - {{studio_name}}',
      variables: ['client_name', 'event_type', 'time_until', 'event_time', 'event_location', 'studio_name'],
      isActive: true,
      usageCount: 28,
      lastUsed: '2024-01-21T14:00:00Z',
      createdAt: '2024-01-01T10:00:00Z',
      updatedAt: '2024-01-10T16:20:00Z'
    }
  ]);

  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         notification.message.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || notification.status === statusFilter;
    const matchesType = typeFilter === "all" || notification.type === typeFilter;
    return matchesSearch && matchesStatus && matchesType;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'secondary';
      case 'scheduled': return 'default';
      case 'sent': return 'default';
      case 'delivered': return 'default';
      case 'failed': return 'destructive';
      default: return 'secondary';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'email': return Mail;
      case 'sms': return MessageSquare;
      case 'push': return Bell;
      case 'in_app': return Bell;
      default: return Bell;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'welcome': return 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300';
      case 'reminder': return 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300';
      case 'update': return 'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300';
      case 'marketing': return 'bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300';
      case 'system': return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  return (
    <StudioLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <Bell className="h-8 w-8 text-blue-600" />
              Notification Center
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Manage automated notifications, templates, and client communication
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={() => setShowTemplateEditor(true)}
            >
              <FileText className="h-4 w-4 mr-2" />
              Manage Templates
            </Button>
            <Button
              onClick={() => setShowCreateNotification(true)}
              className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
            >
              <Plus className="h-4 w-4 mr-2" />
              Send Notification
            </Button>
          </div>
        </div>

        {/* Notification Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <Send className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {notifications.length}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Total Sent</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {notifications.filter(n => n.status === 'delivered').length}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Delivered</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <FileText className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {templates.length}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Templates</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                  <Zap className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    2
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Active Rules</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="automation">Automation</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          {/* Notifications Tab */}
          <TabsContent value="notifications" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5 text-blue-600" />
                  Recent Notifications ({filteredNotifications.length})
                </CardTitle>
                <CardDescription>
                  Monitor sent notifications and their delivery status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredNotifications.map((notification) => {
                    const TypeIcon = getTypeIcon(notification.type);
                    return (
                      <div key={notification.id} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <div className="h-10 w-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                              <TypeIcon className="h-5 w-5 text-blue-600" />
                            </div>
                            <div>
                              <div className="flex items-center gap-2">
                                <h3 className="font-semibold text-gray-900 dark:text-white">
                                  {notification.title}
                                </h3>
                                <Badge variant={getStatusColor(notification.status)}>
                                  {notification.status}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                {notification.message}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {notification.sentAt ? timeAgo(notification.sentAt) : timeAgo(notification.createdAt)}
                            </p>
                            <p className="text-xs text-gray-400">
                              by {notification.createdBy}
                            </p>
                          </div>
                        </div>

                        {/* Notification Stats */}
                        <div className="grid grid-cols-4 gap-4 p-3 bg-white dark:bg-gray-800 rounded-lg">
                          <div className="text-center">
                            <div className="text-lg font-bold text-blue-600">
                              {notification.deliveredCount}
                            </div>
                            <div className="text-xs text-gray-500">Delivered</div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-bold text-green-600">
                              {notification.openedCount}
                            </div>
                            <div className="text-xs text-gray-500">Opened</div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-bold text-purple-600">
                              {notification.clickedCount}
                            </div>
                            <div className="text-xs text-gray-500">Clicked</div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-bold text-red-600">
                              {notification.failedCount}
                            </div>
                            <div className="text-xs text-gray-500">Failed</div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </StudioLayout>
  );
}
                </CardTitle>
                <CardDescription>
                  Stay informed about important events and updates
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Filters */}
            <div className="flex flex-col lg:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    placeholder="Search notifications..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 h-11 bg-gray-50 dark:bg-gray-700"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">All Categories</option>
                  <option value="client">Clients</option>
                  <option value="upload">Uploads</option>
                  <option value="download">Downloads</option>
                  <option value="security">Security</option>
                  <option value="billing">Billing</option>
                  <option value="system">System</option>
                </select>
                <Button
                  variant={showUnreadOnly ? "default" : "outline"}
                  size="sm"
                  onClick={() => setShowUnreadOnly(!showUnreadOnly)}
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Unread Only
                </Button>
              </div>
            </div>

            {/* Notifications */}
            <div className="space-y-4">
              {filteredNotifications.map((notification) => {
                const Icon = getNotificationIcon(notification.type, notification.category);
                return (
                  <div
                    key={notification.id}
                    className={cn(
                      "flex items-start gap-4 p-4 rounded-lg border transition-all duration-200",
                      notification.isRead
                        ? "border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800"
                        : "border-blue-200 dark:border-blue-600 bg-blue-50 dark:bg-blue-900/10"
                    )}
                  >
                    <div className={cn(
                      "h-10 w-10 rounded-lg flex items-center justify-center flex-shrink-0",
                      getNotificationColor(notification.type)
                    )}>
                      <Icon className="h-5 w-5" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-1">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold text-gray-900 dark:text-white">
                            {notification.title}
                          </h3>
                          <Badge variant={getTypeColor(notification.type) as any} className="text-xs">
                            {notification.category}
                          </Badge>
                          {!notification.isRead && (
                            <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                          )}
                        </div>
                        <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                          <Clock className="h-3 w-3" />
                          {timeAgo(notification.timestamp)}
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                        {notification.message}
                      </p>
                      <div className="flex items-center gap-2">
                        {notification.actionUrl && (
                          <Button variant="outline" size="sm">
                            {notification.actionText || 'View'}
                          </Button>
                        )}
                        {!notification.isRead && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => markAsRead(notification.id)}
                          >
                            <Check className="h-4 w-4 mr-2" />
                            Mark Read
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deleteNotification(notification.id)}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Empty State */}
            {filteredNotifications.length === 0 && (
              <div className="text-center py-12">
                <Bell className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  No notifications found
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  {searchTerm || filterType !== "all" || showUnreadOnly
                    ? "Try adjusting your search or filter criteria."
                    : "You're all caught up! No new notifications."
                  }
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </StudioLayout>
  );
}
