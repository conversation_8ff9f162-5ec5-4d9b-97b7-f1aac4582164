import { prisma } from '@/lib/db';

export class AnalyticsService {
  // Get comprehensive analytics for admin dashboard
  static async getAdminAnalytics(period: number = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - period);

      const [
        totalStudios,
        activeStudios,
        totalClients,
        totalPhotos,
        totalRevenue,
        storageUsage,
        registrationTrends,
        revenueTrends,
        topStudios,
        systemHealth,
      ] = await Promise.all([
        prisma.studio.count(),
        prisma.studio.count({ where: { isActive: true, isApproved: true } }),
        prisma.client.count(),
        prisma.photo.count(),
        prisma.billingHistory.aggregate({
          where: {
            status: 'completed',
            createdAt: { gte: startDate },
          },
          _sum: { amount: true },
        }),
        prisma.studio.aggregate({
          _sum: { storageUsed: true },
        }),
        this.getRegistrationTrends(startDate),
        this.getRevenueTrends(startDate),
        this.getTopStudios(10),
        this.getSystemHealth(),
      ]);

      return {
        overview: {
          totalStudios,
          activeStudios,
          pendingStudios: totalStudios - activeStudios,
          totalClients,
          totalPhotos,
          totalRevenue: totalRevenue._sum.amount?.toString() || '0',
          totalStorage: storageUsage._sum.storageUsed?.toString() || '0',
        },
        trends: {
          registrationTrends,
          revenueTrends,
          period,
        },
        topStudios,
        systemHealth,
      };
    } catch (error) {
      console.error('Error fetching admin analytics:', error);
      throw new Error('Failed to fetch admin analytics');
    }
  }

  // Get studio-specific analytics
  static async getStudioAnalytics(studioId: string, period: number = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - period);

      const [
        totalClients,
        activeClients,
        totalPhotos,
        matchedPhotos,
        totalDownloads,
        totalFavorites,
        uploadTrends,
        clientEngagement,
        topClients,
        eventStats,
      ] = await Promise.all([
        prisma.client.count({ where: { studioId } }),
        prisma.client.count({ where: { studioId, isActive: true } }),
        prisma.photo.count({ where: { studioId } }),
        prisma.photo.count({ where: { studioId, isMatched: true } }),
        prisma.accessLog.count({
          where: {
            action: 'download',
            createdAt: { gte: startDate },
            client: { studioId },
          },
        }),
        prisma.favorite.count({
          where: {
            createdAt: { gte: startDate },
            client: { studioId },
          },
        }),
        this.getUploadTrends(studioId, startDate),
        this.getClientEngagement(studioId, startDate),
        this.getTopClients(studioId, 10),
        this.getEventStats(studioId),
      ]);

      const engagementRate = totalClients > 0 ? (activeClients / totalClients) * 100 : 0;
      const matchingRate = totalPhotos > 0 ? (matchedPhotos / totalPhotos) * 100 : 0;
      const favoriteRate = matchedPhotos > 0 ? (totalFavorites / matchedPhotos) * 100 : 0;

      return {
        overview: {
          totalClients,
          activeClients,
          totalPhotos,
          matchedPhotos,
          unmatchedPhotos: totalPhotos - matchedPhotos,
          totalDownloads,
          totalFavorites,
        },
        metrics: {
          engagementRate: Math.round(engagementRate * 100) / 100,
          matchingRate: Math.round(matchingRate * 100) / 100,
          favoriteRate: Math.round(favoriteRate * 100) / 100,
          avgPhotosPerClient: totalClients > 0 ? Math.round(matchedPhotos / totalClients) : 0,
        },
        trends: {
          uploadTrends,
          clientEngagement,
          period,
        },
        topClients,
        eventStats,
      };
    } catch (error) {
      console.error('Error fetching studio analytics:', error);
      throw new Error('Failed to fetch studio analytics');
    }
  }

  // Get client-specific analytics
  static async getClientAnalytics(clientId: string, period: number = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - period);

      const [
        totalPhotos,
        totalFavorites,
        totalComments,
        totalDownloads,
        activityTrends,
        eventBreakdown,
      ] = await Promise.all([
        prisma.photo.count({
          where: { clientId, isMatched: true },
        }),
        prisma.favorite.count({
          where: { clientId },
        }),
        prisma.comment.count({
          where: { clientId },
        }),
        prisma.accessLog.count({
          where: { clientId, action: 'download' },
        }),
        this.getClientActivityTrends(clientId, startDate),
        this.getClientEventBreakdown(clientId),
      ]);

      return {
        overview: {
          totalPhotos,
          totalFavorites,
          totalComments,
          totalDownloads,
        },
        trends: {
          activityTrends,
          period,
        },
        eventBreakdown,
      };
    } catch (error) {
      console.error('Error fetching client analytics:', error);
      throw new Error('Failed to fetch client analytics');
    }
  }

  // Helper methods for specific analytics

  private static async getRegistrationTrends(startDate: Date) {
    try {
      const trends = await prisma.$queryRaw`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as count
        FROM studios 
        WHERE created_at >= ${startDate}
        GROUP BY DATE(created_at)
        ORDER BY date ASC
      `;
      return trends;
    } catch (error) {
      console.error('Error fetching registration trends:', error);
      return [];
    }
  }

  private static async getRevenueTrends(startDate: Date) {
    try {
      const trends = await prisma.$queryRaw`
        SELECT 
          DATE(created_at) as date,
          SUM(amount) as revenue
        FROM billing_history 
        WHERE created_at >= ${startDate} AND status = 'completed'
        GROUP BY DATE(created_at)
        ORDER BY date ASC
      `;
      return trends;
    } catch (error) {
      console.error('Error fetching revenue trends:', error);
      return [];
    }
  }

  private static async getTopStudios(limit: number) {
    try {
      return await prisma.studio.findMany({
        take: limit,
        orderBy: {
          clients: {
            _count: 'desc',
          },
        },
        include: {
          _count: {
            select: {
              clients: true,
              photos: true,
            },
          },
          subscription: {
            include: {
              plan: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
      });
    } catch (error) {
      console.error('Error fetching top studios:', error);
      return [];
    }
  }

  private static async getSystemHealth() {
    try {
      const [
        totalStorage,
        activeConnections,
        errorRate,
        avgResponseTime,
      ] = await Promise.all([
        prisma.studio.aggregate({
          _sum: { storageUsed: true },
        }),
        prisma.client.count({
          where: {
            lastAccess: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
            },
          },
        }),
        // Placeholder for error rate calculation
        Promise.resolve(0.5),
        // Placeholder for average response time
        Promise.resolve(150),
      ]);

      return {
        totalStorage: totalStorage._sum.storageUsed?.toString() || '0',
        activeConnections,
        errorRate,
        avgResponseTime,
        status: 'healthy', // This would be calculated based on various metrics
      };
    } catch (error) {
      console.error('Error fetching system health:', error);
      return {
        totalStorage: '0',
        activeConnections: 0,
        errorRate: 0,
        avgResponseTime: 0,
        status: 'unknown',
      };
    }
  }

  private static async getUploadTrends(studioId: string, startDate: Date) {
    try {
      const trends = await prisma.$queryRaw`
        SELECT 
          DATE(uploaded_at) as date,
          COUNT(*) as count,
          SUM(size) as total_size
        FROM photos 
        WHERE studio_id = ${studioId} AND uploaded_at >= ${startDate}
        GROUP BY DATE(uploaded_at)
        ORDER BY date ASC
      `;
      return trends;
    } catch (error) {
      console.error('Error fetching upload trends:', error);
      return [];
    }
  }

  private static async getClientEngagement(studioId: string, startDate: Date) {
    try {
      return await prisma.client.findMany({
        where: { studioId },
        select: {
          id: true,
          name: true,
          _count: {
            select: {
              photos: { where: { isMatched: true } },
              favorites: { where: { createdAt: { gte: startDate } } },
              accessLogs: { where: { createdAt: { gte: startDate } } },
            },
          },
        },
        orderBy: {
          accessLogs: {
            _count: 'desc',
          },
        },
        take: 10,
      });
    } catch (error) {
      console.error('Error fetching client engagement:', error);
      return [];
    }
  }

  private static async getTopClients(studioId: string, limit: number) {
    try {
      return await prisma.client.findMany({
        where: { studioId },
        take: limit,
        orderBy: {
          photos: {
            _count: 'desc',
          },
        },
        include: {
          _count: {
            select: {
              photos: { where: { isMatched: true } },
              favorites: true,
              comments: true,
              accessLogs: true,
            },
          },
        },
      });
    } catch (error) {
      console.error('Error fetching top clients:', error);
      return [];
    }
  }

  private static async getEventStats(studioId: string) {
    try {
      return await prisma.event.findMany({
        where: { studioId },
        include: {
          _count: {
            select: {
              photos: true,
            },
          },
        },
        orderBy: {
          photos: {
            _count: 'desc',
          },
        },
        take: 10,
      });
    } catch (error) {
      console.error('Error fetching event stats:', error);
      return [];
    }
  }

  private static async getClientActivityTrends(clientId: string, startDate: Date) {
    try {
      const trends = await prisma.$queryRaw`
        SELECT 
          DATE(created_at) as date,
          action,
          COUNT(*) as count
        FROM access_logs 
        WHERE client_id = ${clientId} AND created_at >= ${startDate}
        GROUP BY DATE(created_at), action
        ORDER BY date ASC
      `;
      return trends;
    } catch (error) {
      console.error('Error fetching client activity trends:', error);
      return [];
    }
  }

  private static async getClientEventBreakdown(clientId: string) {
    try {
      return await prisma.event.findMany({
        where: {
          photos: {
            some: {
              clientId,
              isMatched: true,
            },
          },
        },
        include: {
          _count: {
            select: {
              photos: {
                where: {
                  clientId,
                  isMatched: true,
                },
              },
            },
          },
        },
        orderBy: {
          date: 'desc',
        },
      });
    } catch (error) {
      console.error('Error fetching client event breakdown:', error);
      return [];
    }
  }
}
