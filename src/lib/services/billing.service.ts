import { prisma } from '@/lib/db';
import { Decimal } from '@prisma/client/runtime/library';

export class BillingService {
  // Get studio billing history
  static async getBillingHistory(studioId: string, params: {
    page?: number;
    limit?: number;
    status?: 'all' | 'pending' | 'completed' | 'failed';
    sortBy?: 'createdAt' | 'amount';
    sortOrder?: 'asc' | 'desc';
  }) {
    try {
      const {
        page = 1,
        limit = 10,
        status = 'all',
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = params;

      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = { studioId };
      if (status !== 'all') {
        where.status = status;
      }

      // Build orderBy clause
      const orderBy: any = {};
      orderBy[sortBy] = sortOrder;

      const [billingHistory, total] = await Promise.all([
        prisma.billingHistory.findMany({
          where,
          skip,
          take: limit,
          orderBy,
        }),
        prisma.billingHistory.count({ where }),
      ]);

      return {
        billingHistory,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching billing history:', error);
      throw new Error('Failed to fetch billing history');
    }
  }

  // Create billing record
  static async createBillingRecord(data: {
    studioId: string;
    amount: number;
    currency?: string;
    description?: string;
    paymentId?: string;
  }) {
    try {
      const billingRecord = await prisma.billingHistory.create({
        data: {
          ...data,
          amount: new Decimal(data.amount),
          currency: data.currency || 'INR',
          status: 'pending',
        },
      });

      return billingRecord;
    } catch (error) {
      console.error('Error creating billing record:', error);
      throw new Error('Failed to create billing record');
    }
  }

  // Update billing record status
  static async updateBillingStatus(
    billingId: string,
    status: 'pending' | 'completed' | 'failed',
    paymentId?: string
  ) {
    try {
      const updatedRecord = await prisma.billingHistory.update({
        where: { id: billingId },
        data: {
          status,
          paymentId,
        },
      });

      return updatedRecord;
    } catch (error) {
      console.error('Error updating billing status:', error);
      throw new Error('Failed to update billing status');
    }
  }

  // Get studio subscription details
  static async getStudioSubscription(studioId: string) {
    try {
      const studio = await prisma.studio.findUnique({
        where: { id: studioId },
        include: {
          subscription: {
            include: {
              plan: true,
            },
          },
        },
      });

      if (!studio) {
        throw new Error('Studio not found');
      }

      return studio.subscription;
    } catch (error) {
      console.error('Error fetching studio subscription:', error);
      throw new Error('Failed to fetch subscription details');
    }
  }

  // Create or update studio subscription
  static async updateStudioSubscription(studioId: string, planId: string, data: {
    startDate: Date;
    endDate: Date;
    autoRenew?: boolean;
    paymentMethod?: string;
  }) {
    try {
      // Check if studio already has a subscription
      const existingSubscription = await prisma.subscription.findFirst({
        where: { studioId },
      });

      let subscription;

      if (existingSubscription) {
        // Update existing subscription
        subscription = await prisma.subscription.update({
          where: { id: existingSubscription.id },
          data: {
            planId,
            status: 'active',
            ...data,
          },
          include: {
            plan: true,
          },
        });
      } else {
        // Create new subscription
        subscription = await prisma.subscription.create({
          data: {
            studioId,
            planId,
            status: 'active',
            ...data,
          },
          include: {
            plan: true,
          },
        });

        // Update studio with subscription ID
        await prisma.studio.update({
          where: { id: studioId },
          data: { subscriptionId: subscription.id },
        });
      }

      return subscription;
    } catch (error) {
      console.error('Error updating studio subscription:', error);
      throw new Error('Failed to update subscription');
    }
  }

  // Cancel studio subscription
  static async cancelSubscription(studioId: string): Promise<{ success: boolean; message: string }> {
    try {
      const subscription = await prisma.subscription.findFirst({
        where: { studioId },
      });

      if (!subscription) {
        return { success: false, message: 'No active subscription found' };
      }

      await prisma.subscription.update({
        where: { id: subscription.id },
        data: {
          status: 'cancelled',
          autoRenew: false,
        },
      });

      // Create notification
      await prisma.notification.create({
        data: {
          studioId,
          type: 'system',
          title: 'Subscription Cancelled',
          message: 'Your subscription has been cancelled. You can continue using the service until the end of your billing period.',
        },
      });

      return { success: true, message: 'Subscription cancelled successfully' };
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      return { success: false, message: 'Failed to cancel subscription' };
    }
  }

  // Get billing analytics for admin
  static async getBillingAnalytics(period: number = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - period);

      const [
        totalRevenue,
        pendingPayments,
        failedPayments,
        completedPayments,
        revenueByPlan,
        revenueByDay,
        topPayingStudios,
      ] = await Promise.all([
        prisma.billingHistory.aggregate({
          where: {
            status: 'completed',
            createdAt: { gte: startDate },
          },
          _sum: { amount: true },
        }),
        prisma.billingHistory.aggregate({
          where: {
            status: 'pending',
            createdAt: { gte: startDate },
          },
          _sum: { amount: true },
          _count: true,
        }),
        prisma.billingHistory.aggregate({
          where: {
            status: 'failed',
            createdAt: { gte: startDate },
          },
          _sum: { amount: true },
          _count: true,
        }),
        prisma.billingHistory.count({
          where: {
            status: 'completed',
            createdAt: { gte: startDate },
          },
        }),
        this.getRevenueByPlan(startDate),
        this.getRevenueByDay(startDate),
        this.getTopPayingStudios(10),
      ]);

      return {
        overview: {
          totalRevenue: totalRevenue._sum.amount?.toString() || '0',
          pendingPayments: pendingPayments._sum.amount?.toString() || '0',
          failedPayments: failedPayments._sum.amount?.toString() || '0',
          completedPayments,
          pendingCount: pendingPayments._count,
          failedCount: failedPayments._count,
        },
        analytics: {
          revenueByPlan,
          revenueByDay,
          topPayingStudios,
        },
        period,
      };
    } catch (error) {
      console.error('Error fetching billing analytics:', error);
      throw new Error('Failed to fetch billing analytics');
    }
  }

  // Get studio billing summary
  static async getStudioBillingSummary(studioId: string) {
    try {
      const [
        totalSpent,
        lastPayment,
        upcomingPayment,
        paymentHistory,
        currentSubscription,
      ] = await Promise.all([
        prisma.billingHistory.aggregate({
          where: {
            studioId,
            status: 'completed',
          },
          _sum: { amount: true },
        }),
        prisma.billingHistory.findFirst({
          where: {
            studioId,
            status: 'completed',
          },
          orderBy: { createdAt: 'desc' },
        }),
        this.getUpcomingPayment(studioId),
        prisma.billingHistory.findMany({
          where: { studioId },
          orderBy: { createdAt: 'desc' },
          take: 5,
        }),
        prisma.subscription.findFirst({
          where: { studioId },
          include: {
            plan: true,
          },
        }),
      ]);

      return {
        summary: {
          totalSpent: totalSpent._sum.amount?.toString() || '0',
          lastPayment,
          upcomingPayment,
        },
        currentSubscription,
        recentPayments: paymentHistory,
      };
    } catch (error) {
      console.error('Error fetching studio billing summary:', error);
      throw new Error('Failed to fetch billing summary');
    }
  }

  // Process subscription renewal
  static async processSubscriptionRenewal(studioId: string): Promise<{ success: boolean; message: string; billingId?: string }> {
    try {
      const subscription = await prisma.subscription.findFirst({
        where: { studioId, status: 'active' },
        include: {
          plan: true,
        },
      });

      if (!subscription) {
        return { success: false, message: 'No active subscription found' };
      }

      if (!subscription.autoRenew) {
        return { success: false, message: 'Auto-renewal is disabled' };
      }

      // Create billing record for renewal
      const billingRecord = await this.createBillingRecord({
        studioId,
        amount: subscription.plan.price.toNumber(),
        description: `Subscription renewal - ${subscription.plan.name}`,
      });

      // Update subscription dates
      const newStartDate = subscription.endDate;
      const newEndDate = new Date(subscription.endDate);
      
      if (subscription.plan.billingCycle === 'monthly') {
        newEndDate.setMonth(newEndDate.getMonth() + 1);
      } else if (subscription.plan.billingCycle === 'yearly') {
        newEndDate.setFullYear(newEndDate.getFullYear() + 1);
      }

      await prisma.subscription.update({
        where: { id: subscription.id },
        data: {
          startDate: newStartDate,
          endDate: newEndDate,
        },
      });

      return {
        success: true,
        message: 'Subscription renewal processed',
        billingId: billingRecord.id,
      };
    } catch (error) {
      console.error('Error processing subscription renewal:', error);
      return { success: false, message: 'Failed to process renewal' };
    }
  }

  // Helper methods

  private static async getRevenueByPlan(startDate: Date) {
    try {
      const result = await prisma.$queryRaw`
        SELECT 
          sp.name as plan_name,
          SUM(bh.amount) as revenue,
          COUNT(bh.id) as transaction_count
        FROM billing_history bh
        JOIN subscriptions s ON bh.studio_id = s.studio_id
        JOIN subscription_plans sp ON s.plan_id = sp.id
        WHERE bh.status = 'completed' AND bh.created_at >= ${startDate}
        GROUP BY sp.name
        ORDER BY revenue DESC
      `;
      return result;
    } catch (error) {
      console.error('Error fetching revenue by plan:', error);
      return [];
    }
  }

  private static async getRevenueByDay(startDate: Date) {
    try {
      const result = await prisma.$queryRaw`
        SELECT 
          DATE(created_at) as date,
          SUM(amount) as revenue,
          COUNT(*) as transaction_count
        FROM billing_history
        WHERE status = 'completed' AND created_at >= ${startDate}
        GROUP BY DATE(created_at)
        ORDER BY date ASC
      `;
      return result;
    } catch (error) {
      console.error('Error fetching revenue by day:', error);
      return [];
    }
  }

  private static async getTopPayingStudios(limit: number) {
    try {
      return await prisma.studio.findMany({
        take: limit,
        include: {
          billingHistory: {
            where: { status: 'completed' },
            select: {
              amount: true,
            },
          },
          subscription: {
            include: {
              plan: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
        orderBy: {
          billingHistory: {
            _count: 'desc',
          },
        },
      });
    } catch (error) {
      console.error('Error fetching top paying studios:', error);
      return [];
    }
  }

  private static async getUpcomingPayment(studioId: string) {
    try {
      const subscription = await prisma.subscription.findFirst({
        where: { studioId, status: 'active' },
        include: {
          plan: true,
        },
      });

      if (!subscription || !subscription.autoRenew) {
        return null;
      }

      return {
        amount: subscription.plan.price,
        dueDate: subscription.endDate,
        planName: subscription.plan.name,
      };
    } catch (error) {
      console.error('Error fetching upcoming payment:', error);
      return null;
    }
  }
}
