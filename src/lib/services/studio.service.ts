import { prisma } from '@/lib/db';
import { generateSecureString, hashPassword } from '@/lib/auth';
import { generateQRCodeData } from '@/lib/utils';

export class StudioService {
  // Get studio dashboard data
  static async getDashboardData(studioId: string) {
    try {
      const [
        studio,
        totalClients,
        activeClients,
        totalPhotos,
        matchedPhotos,
        totalEvents,
        recentActivity,
        storageStats,
      ] = await Promise.all([
        prisma.studio.findUnique({
          where: { id: studioId },
          include: {
            subscription: {
              include: {
                plan: true,
              },
            },
          },
        }),
        prisma.client.count({ where: { studioId } }),
        prisma.client.count({ where: { studioId, isActive: true } }),
        prisma.photo.count({ where: { studioId } }),
        prisma.photo.count({ where: { studioId, isMatched: true } }),
        prisma.event.count({ where: { studioId } }),
        prisma.photo.findMany({
          where: { studioId },
          orderBy: { uploadedAt: 'desc' },
          take: 10,
          include: {
            client: {
              select: { name: true },
            },
            event: {
              select: { name: true },
            },
          },
        }),
        prisma.studio.findUnique({
          where: { id: studioId },
          select: {
            storageUsed: true,
            subscription: {
              select: {
                plan: {
                  select: {
                    storageLimit: true,
                  },
                },
              },
            },
          },
        }),
      ]);

      if (!studio) {
        throw new Error('Studio not found');
      }

      const storageLimit = storageStats?.subscription?.plan?.storageLimit || BigInt(0);
      const storageUsed = storageStats?.storageUsed || BigInt(0);
      const storagePercentage = storageLimit > 0 
        ? Number((storageUsed * BigInt(100)) / storageLimit)
        : 0;

      return {
        studio,
        stats: {
          totalClients,
          activeClients,
          totalPhotos,
          matchedPhotos,
          unmatchedPhotos: totalPhotos - matchedPhotos,
          totalEvents,
          storageUsed: storageUsed.toString(),
          storageLimit: storageLimit.toString(),
          storagePercentage,
        },
        recentActivity,
      };
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      throw new Error('Failed to fetch dashboard data');
    }
  }

  // Get studio clients with pagination and filters
  static async getClients(studioId: string, params: {
    page?: number;
    limit?: number;
    search?: string;
    status?: 'all' | 'active' | 'inactive';
    sortBy?: 'name' | 'createdAt' | 'lastAccess';
    sortOrder?: 'asc' | 'desc';
  }) {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        status = 'all',
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = params;

      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = { studioId };
      
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (status !== 'all') {
        where.isActive = status === 'active';
      }

      // Build orderBy clause
      const orderBy: any = {};
      orderBy[sortBy] = sortOrder;

      const [clients, total] = await Promise.all([
        prisma.client.findMany({
          where,
          skip,
          take: limit,
          orderBy,
          include: {
            _count: {
              select: {
                photos: true,
                favorites: true,
                comments: true,
              },
            },
          },
        }),
        prisma.client.count({ where }),
      ]);

      return {
        clients,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching clients:', error);
      throw new Error('Failed to fetch clients');
    }
  }

  // Create new client
  static async createClient(studioId: string, data: {
    name: string;
    email: string;
    phone?: string;
    password: string;
  }) {
    try {
      // Check if client already exists for this studio
      const existingClient = await prisma.client.findUnique({
        where: {
          email_studioId: {
            email: data.email,
            studioId,
          },
        },
      });

      if (existingClient) {
        throw new Error('Client with this email already exists');
      }

      // Generate unique access codes
      const qrCode = generateSecureString(12);
      const accessLink = generateSecureString(16);
      const hashedPassword = await hashPassword(data.password);

      const client = await prisma.client.create({
        data: {
          ...data,
          password: hashedPassword,
          studioId,
          qrCode,
          accessLink,
        },
        include: {
          _count: {
            select: {
              photos: true,
              favorites: true,
              comments: true,
            },
          },
        },
      });

      return client;
    } catch (error) {
      console.error('Error creating client:', error);
      throw new Error('Failed to create client');
    }
  }

  // Update client
  static async updateClient(studioId: string, clientId: string, data: {
    name?: string;
    email?: string;
    phone?: string;
    isActive?: boolean;
  }) {
    try {
      // Verify client belongs to studio
      const existingClient = await prisma.client.findFirst({
        where: { id: clientId, studioId },
      });

      if (!existingClient) {
        throw new Error('Client not found');
      }

      // If email is being updated, check for duplicates
      if (data.email && data.email !== existingClient.email) {
        const duplicateClient = await prisma.client.findUnique({
          where: {
            email_studioId: {
              email: data.email,
              studioId,
            },
          },
        });

        if (duplicateClient) {
          throw new Error('Client with this email already exists');
        }
      }

      const updatedClient = await prisma.client.update({
        where: { id: clientId },
        data,
        include: {
          _count: {
            select: {
              photos: true,
              favorites: true,
              comments: true,
            },
          },
        },
      });

      return updatedClient;
    } catch (error) {
      console.error('Error updating client:', error);
      throw new Error('Failed to update client');
    }
  }

  // Delete client
  static async deleteClient(studioId: string, clientId: string): Promise<{ success: boolean; message: string }> {
    try {
      // Verify client belongs to studio
      const client = await prisma.client.findFirst({
        where: { id: clientId, studioId },
      });

      if (!client) {
        return { success: false, message: 'Client not found' };
      }

      // Delete client (cascade will handle related records)
      await prisma.client.delete({
        where: { id: clientId },
      });

      return { success: true, message: 'Client deleted successfully' };
    } catch (error) {
      console.error('Error deleting client:', error);
      return { success: false, message: 'Failed to delete client' };
    }
  }

  // Get studio events
  static async getEvents(studioId: string, params: {
    page?: number;
    limit?: number;
    search?: string;
    status?: 'all' | 'active' | 'inactive';
    sortBy?: 'name' | 'date' | 'createdAt';
    sortOrder?: 'asc' | 'desc';
  }) {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        status = 'all',
        sortBy = 'date',
        sortOrder = 'desc',
      } = params;

      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = { studioId };
      
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { location: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (status !== 'all') {
        where.isActive = status === 'active';
      }

      // Build orderBy clause
      const orderBy: any = {};
      orderBy[sortBy] = sortOrder;

      const [events, total] = await Promise.all([
        prisma.event.findMany({
          where,
          skip,
          take: limit,
          orderBy,
          include: {
            _count: {
              select: {
                photos: true,
              },
            },
          },
        }),
        prisma.event.count({ where }),
      ]);

      return {
        events,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching events:', error);
      throw new Error('Failed to fetch events');
    }
  }

  // Create new event
  static async createEvent(studioId: string, data: {
    name: string;
    description?: string;
    date: Date;
    location?: string;
  }) {
    try {
      const event = await prisma.event.create({
        data: {
          ...data,
          studioId,
        },
        include: {
          _count: {
            select: {
              photos: true,
            },
          },
        },
      });

      return event;
    } catch (error) {
      console.error('Error creating event:', error);
      throw new Error('Failed to create event');
    }
  }

  // Update event
  static async updateEvent(studioId: string, eventId: string, data: {
    name?: string;
    description?: string;
    date?: Date;
    location?: string;
    isActive?: boolean;
  }) {
    try {
      // Verify event belongs to studio
      const existingEvent = await prisma.event.findFirst({
        where: { id: eventId, studioId },
      });

      if (!existingEvent) {
        throw new Error('Event not found');
      }

      const updatedEvent = await prisma.event.update({
        where: { id: eventId },
        data,
        include: {
          _count: {
            select: {
              photos: true,
            },
          },
        },
      });

      return updatedEvent;
    } catch (error) {
      console.error('Error updating event:', error);
      throw new Error('Failed to update event');
    }
  }

  // Delete event
  static async deleteEvent(studioId: string, eventId: string): Promise<{ success: boolean; message: string }> {
    try {
      // Verify event belongs to studio
      const event = await prisma.event.findFirst({
        where: { id: eventId, studioId },
      });

      if (!event) {
        return { success: false, message: 'Event not found' };
      }

      // Check if event has photos
      const photoCount = await prisma.photo.count({
        where: { eventId },
      });

      if (photoCount > 0) {
        return { success: false, message: 'Cannot delete event with associated photos' };
      }

      // Delete event
      await prisma.event.delete({
        where: { id: eventId },
      });

      return { success: true, message: 'Event deleted successfully' };
    } catch (error) {
      console.error('Error deleting event:', error);
      return { success: false, message: 'Failed to delete event' };
    }
  }

  // Get studio analytics
  static async getAnalytics(studioId: string, period: number = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - period);

      const [
        totalClients,
        activeClients,
        totalPhotos,
        matchedPhotos,
        totalDownloads,
        totalFavorites,
        clientEngagement,
        uploadTrends,
        topClients,
      ] = await Promise.all([
        prisma.client.count({ where: { studioId } }),
        prisma.client.count({ where: { studioId, isActive: true } }),
        prisma.photo.count({ where: { studioId } }),
        prisma.photo.count({ where: { studioId, isMatched: true } }),
        prisma.accessLog.count({
          where: {
            action: 'download',
            createdAt: { gte: startDate },
            client: { studioId },
          },
        }),
        prisma.favorite.count({
          where: {
            createdAt: { gte: startDate },
            client: { studioId },
          },
        }),
        prisma.client.findMany({
          where: { studioId },
          select: {
            id: true,
            name: true,
            _count: {
              select: {
                photos: true,
                favorites: true,
                accessLogs: {
                  where: { createdAt: { gte: startDate } },
                },
              },
            },
          },
          orderBy: {
            photos: {
              _count: 'desc',
            },
          },
          take: 10,
        }),
        prisma.$queryRaw`
          SELECT 
            DATE(uploaded_at) as date,
            COUNT(*) as count
          FROM photos 
          WHERE studio_id = ${studioId} AND uploaded_at >= ${startDate}
          GROUP BY DATE(uploaded_at)
          ORDER BY date ASC
        `,
        prisma.client.findMany({
          where: { studioId },
          select: {
            id: true,
            name: true,
            email: true,
            _count: {
              select: {
                photos: { where: { isMatched: true } },
                favorites: true,
                accessLogs: {
                  where: { createdAt: { gte: startDate } },
                },
              },
            },
          },
          orderBy: {
            accessLogs: {
              _count: 'desc',
            },
          },
          take: 5,
        }),
      ]);

      const engagementRate = totalClients > 0 ? (activeClients / totalClients) * 100 : 0;
      const matchingRate = totalPhotos > 0 ? (matchedPhotos / totalPhotos) * 100 : 0;
      const favoriteRate = matchedPhotos > 0 ? (totalFavorites / matchedPhotos) * 100 : 0;

      return {
        overview: {
          totalClients,
          activeClients,
          totalPhotos,
          matchedPhotos,
          unmatchedPhotos: totalPhotos - matchedPhotos,
          totalDownloads,
          totalFavorites,
        },
        metrics: {
          engagementRate: Math.round(engagementRate * 100) / 100,
          matchingRate: Math.round(matchingRate * 100) / 100,
          favoriteRate: Math.round(favoriteRate * 100) / 100,
          avgPhotosPerClient: totalClients > 0 ? Math.round(matchedPhotos / totalClients) : 0,
        },
        clientEngagement,
        uploadTrends,
        topClients,
        period,
      };
    } catch (error) {
      console.error('Error fetching studio analytics:', error);
      throw new Error('Failed to fetch studio analytics');
    }
  }
}
