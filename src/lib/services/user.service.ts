import { prisma } from '@/lib/db';
import { hashPassword, verifyPassword } from '@/lib/auth';
import { User, Admin, Studio, Client } from '@/types';

export class UserService {
  // Get user by ID and role
  static async getUserById(id: string, role: 'admin' | 'studio' | 'client'): Promise<User | null> {
    try {
      switch (role) {
        case 'admin':
          return await prisma.admin.findUnique({
            where: { id },
          });
        case 'studio':
          return await prisma.studio.findUnique({
            where: { id },
            include: {
              subscription: {
                include: {
                  plan: true,
                },
              },
            },
          });
        case 'client':
          return await prisma.client.findUnique({
            where: { id },
            include: {
              studio: true,
            },
          });
        default:
          return null;
      }
    } catch (error) {
      console.error('Error fetching user:', error);
      return null;
    }
  }

  // Get user by email and role
  static async getUserByEmail(email: string, role: 'admin' | 'studio' | 'client'): Promise<User | null> {
    try {
      switch (role) {
        case 'admin':
          return await prisma.admin.findUnique({
            where: { email },
          });
        case 'studio':
          return await prisma.studio.findUnique({
            where: { email },
            include: {
              subscription: {
                include: {
                  plan: true,
                },
              },
            },
          });
        case 'client':
          // For clients, we need studioId as well since email is not unique across studios
          return null; // Use getClientByEmailAndStudio instead
        default:
          return null;
      }
    } catch (error) {
      console.error('Error fetching user by email:', error);
      return null;
    }
  }

  // Update user profile
  static async updateUserProfile(
    id: string,
    role: 'admin' | 'studio' | 'client',
    data: Partial<User>
  ): Promise<User | null> {
    try {
      switch (role) {
        case 'admin':
          return await prisma.admin.update({
            where: { id },
            data: {
              name: data.name,
              email: data.email,
            },
          });
        case 'studio':
          return await prisma.studio.update({
            where: { id },
            data: {
              name: data.name,
              email: data.email,
              businessName: (data as Studio).businessName,
              phone: (data as Studio).phone,
              address: (data as Studio).address,
            },
            include: {
              subscription: {
                include: {
                  plan: true,
                },
              },
            },
          });
        case 'client':
          return await prisma.client.update({
            where: { id },
            data: {
              name: data.name,
              email: data.email,
              phone: (data as Client).phone,
            },
            include: {
              studio: true,
            },
          });
        default:
          return null;
      }
    } catch (error) {
      console.error('Error updating user profile:', error);
      return null;
    }
  }

  // Change user password
  static async changePassword(
    id: string,
    role: 'admin' | 'studio' | 'client',
    currentPassword: string,
    newPassword: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Get current user
      const user = await this.getUserById(id, role);
      if (!user) {
        return { success: false, message: 'User not found' };
      }

      // Verify current password
      const isValidPassword = await verifyPassword(currentPassword, user.password);
      if (!isValidPassword) {
        return { success: false, message: 'Current password is incorrect' };
      }

      // Hash new password
      const hashedNewPassword = await hashPassword(newPassword);

      // Update password
      switch (role) {
        case 'admin':
          await prisma.admin.update({
            where: { id },
            data: { password: hashedNewPassword },
          });
          break;
        case 'studio':
          await prisma.studio.update({
            where: { id },
            data: { password: hashedNewPassword },
          });
          break;
        case 'client':
          await prisma.client.update({
            where: { id },
            data: { password: hashedNewPassword },
          });
          break;
      }

      return { success: true, message: 'Password updated successfully' };
    } catch (error) {
      console.error('Error changing password:', error);
      return { success: false, message: 'Failed to update password' };
    }
  }

  // Get client by email and studio
  static async getClientByEmailAndStudio(email: string, studioId: string): Promise<Client | null> {
    try {
      return await prisma.client.findUnique({
        where: {
          email_studioId: {
            email,
            studioId,
          },
        },
        include: {
          studio: true,
        },
      });
    } catch (error) {
      console.error('Error fetching client:', error);
      return null;
    }
  }

  // Deactivate user account
  static async deactivateUser(
    id: string,
    role: 'admin' | 'studio' | 'client'
  ): Promise<{ success: boolean; message: string }> {
    try {
      switch (role) {
        case 'studio':
          await prisma.studio.update({
            where: { id },
            data: { isActive: false },
          });
          break;
        case 'client':
          await prisma.client.update({
            where: { id },
            data: { isActive: false },
          });
          break;
        default:
          return { success: false, message: 'Cannot deactivate admin accounts' };
      }

      return { success: true, message: 'Account deactivated successfully' };
    } catch (error) {
      console.error('Error deactivating user:', error);
      return { success: false, message: 'Failed to deactivate account' };
    }
  }

  // Reactivate user account
  static async reactivateUser(
    id: string,
    role: 'admin' | 'studio' | 'client'
  ): Promise<{ success: boolean; message: string }> {
    try {
      switch (role) {
        case 'studio':
          await prisma.studio.update({
            where: { id },
            data: { isActive: true },
          });
          break;
        case 'client':
          await prisma.client.update({
            where: { id },
            data: { isActive: true },
          });
          break;
        default:
          return { success: false, message: 'Cannot modify admin accounts' };
      }

      return { success: true, message: 'Account reactivated successfully' };
    } catch (error) {
      console.error('Error reactivating user:', error);
      return { success: false, message: 'Failed to reactivate account' };
    }
  }

  // Delete user account (soft delete by deactivation)
  static async deleteUser(
    id: string,
    role: 'admin' | 'studio' | 'client'
  ): Promise<{ success: boolean; message: string }> {
    try {
      // For now, we'll use soft delete (deactivation)
      // In the future, we might implement hard delete with proper cleanup
      return await this.deactivateUser(id, role);
    } catch (error) {
      console.error('Error deleting user:', error);
      return { success: false, message: 'Failed to delete account' };
    }
  }
}
